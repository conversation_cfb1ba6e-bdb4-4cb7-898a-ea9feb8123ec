// @ts-ignore
import { request, useNotification } from '@strapi/helper-plugin';
import React, { useEffect, useState } from 'react';

const SettingsPage = () => {
  const [pageTitle, setPageTitle] = useState('');
  const [pageSubtitle, setPageSubtitle] = useState('');
  const [favicon, setFavicon] = useState('');
  const toggleNotification = useNotification();

  useEffect(() => {
    request('/ui-settings/config', { method: 'GET' }).then((res) => {
      setPageTitle(res.pageTitle || '');
      setPageSubtitle(res.pageSubtitle || '');
      setFavicon(res.favicon || '');
    });
  }, []);

  const handleSave = async () => {
    try {
      await request('/ui-settings/config', {
        method: 'POST',
        body: { pageTitle, pageSubtitle, favicon },
      });
      toggleNotification({ type: 'success', message: 'Settings saved!' });
    } catch (e) {
      toggleNotification({ type: 'warning', message: 'Error saving settings' });
    }
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>UI Settings</h1>
      <div style={{ marginBottom: '1rem' }}>
        <label>Page Title</label>
        <br />
        <input value={pageTitle} onChange={(e) => setPageTitle(e.target.value)} />
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <label>Page Subtitle</label>
        <br />
        <input value={pageSubtitle} onChange={(e) => setPageSubtitle(e.target.value)} />
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <label>Favicon URL</label>
        <br />
        <input value={favicon} onChange={(e) => setFavicon(e.target.value)} />
      </div>
      <button onClick={handleSave}>Save</button>
    </div>
  );
};

export default SettingsPage;
