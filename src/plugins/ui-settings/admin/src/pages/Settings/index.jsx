import { useFetchClient, useNotification } from '@strapi/strapi/admin';
import React, { useEffect, useState } from 'react';

const SettingsPage = () => {
  const [pageTitle, setPageTitle] = useState('');
  const [pageSubtitle, setPageSubtitle] = useState('');
  const [favicon, setFavicon] = useState('');
  const { get, post } = useFetchClient();
  const toggleNotification = useNotification();

  useEffect(() => {
    get('/ui-settings/config')
      .then((res) => {
        setPageTitle(res.data.pageTitle || '');
        setPageSubtitle(res.data.pageSubtitle || '');
        setFavicon(res.data.favicon || '');
      })
      .catch((error) => {
        console.error('Error loading settings:', error);
      });
  }, [get]);

  const handleSave = async () => {
    try {
      await post('/ui-settings/config', {
        pageTitle,
        pageSubtitle,
        favicon,
      });
      toggleNotification({ type: 'success', message: 'Settings saved!' });
    } catch (e) {
      console.error('Error saving settings:', e);
      toggleNotification({ type: 'warning', message: 'Error saving settings' });
    }
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>UI Settings</h1>
      <div style={{ marginBottom: '1rem' }}>
        <label>Page Title</label>
        <br />
        <input value={pageTitle} onChange={(e) => setPageTitle(e.target.value)} />
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <label>Page Subtitle</label>
        <br />
        <input value={pageSubtitle} onChange={(e) => setPageSubtitle(e.target.value)} />
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <label>Favicon URL</label>
        <br />
        <input value={favicon} onChange={(e) => setFavicon(e.target.value)} />
      </div>
      <button onClick={handleSave}>Save</button>
    </div>
  );
};

export default SettingsPage;
