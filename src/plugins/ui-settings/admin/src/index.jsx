import SettingsPage from './pages/Settings/index.jsx';

const pluginId = 'ui-settings';

console.log('UI Settings plugin file loaded');

const plugin = {
  register(app) {
    console.log('UI Settings: register called', app);

    app.addMenuLink({
      to: `/plugins/${pluginId}`,
      icon: () => '⚙️',
      intlLabel: {
        id: 'ui-settings.plugin.name',
        defaultMessage: 'UI Settings',
      },
      Component: SettingsPage,
      permissions: [],
    });

    console.log('UI Settings: menu link added');
  },

  bootstrap(app) {
    console.log('UI Settings: bootstrap called', app);
  },
};

console.log('UI Settings: exporting plugin', plugin);

export default plugin;
