import React from 'react';

const pluginId = 'ui-settings';

// Simple settings component
const SettingsPage = () => {
  const [pageTitle, setPageTitle] = React.useState('');
  const [favicon, setFavicon] = React.useState('');
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    // Load settings
    fetch('/ui-settings/config')
      .then((res) => res.json())
      .then((data) => {
        setPageTitle(data.pageTitle || '');
        setFavicon(data.favicon || '');
      })
      .catch(console.error);
  }, []);

  const handleSave = async () => {
    setLoading(true);
    try {
      await fetch('/ui-settings/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pageTitle, favicon }),
      });
      alert('Settings saved!');
    } catch (error) {
      alert('Error saving settings');
      console.error(error);
    }
    setLoading(false);
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>UI Settings</h1>
      <div style={{ marginBottom: '1rem' }}>
        <label>Page Title:</label>
        <br />
        <input
          value={pageTitle}
          onChange={(e) => setPageTitle(e.target.value)}
          style={{ width: '300px', padding: '8px' }}
        />
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <label>Favicon URL:</label>
        <br />
        <input
          value={favicon}
          onChange={(e) => setFavicon(e.target.value)}
          style={{ width: '300px', padding: '8px' }}
        />
      </div>
      <button onClick={handleSave} disabled={loading}>
        {loading ? 'Saving...' : 'Save Settings'}
      </button>
    </div>
  );
};

console.log('UI Settings admin plugin loading...');

export default {
  register(app) {
    console.log('UI Settings register called with app:', app);
    console.log('Available methods:', Object.keys(app));

    // Try multiple registration methods
    if (app.addMenuLink) {
      app.addMenuLink({
        to: `/plugins/${pluginId}`,
        icon: () => React.createElement('span', {}, '⚙️'),
        intlLabel: {
          id: 'ui-settings.plugin.name',
          defaultMessage: 'UI Settings',
        },
        Component: SettingsPage,
        permissions: [],
      });
      console.log('Menu link added via addMenuLink');
    }

    if (app.addSettingsLink) {
      app.addSettingsLink({
        id: pluginId,
        to: `/settings/${pluginId}`,
        intlLabel: {
          id: 'ui-settings.plugin.name',
          defaultMessage: 'UI Settings',
        },
        Component: SettingsPage,
      });
      console.log('Settings link added via addSettingsLink');
    }
  },

  bootstrap(app) {
    console.log('UI Settings bootstrap called with app:', app);
  },
};
