import SettingsPage from './pages/Settings';
import pluginId from './pluginId';

export default {
  register(app) {
    app.createSettingSection(
      {
        id: pluginId,
        intlLabel: {
          id: `${pluginId}.plugin.name`,
          defaultMessage: 'UI Settings',
        },
      },
      [
        {
          intlLabel: {
            id: `${pluginId}.plugin.name`,
            defaultMessage: 'Configuration',
          },
          id: 'settings',
          to: `/settings/${pluginId}`,
          Component: SettingsPage,
        },
      ]
    );

    app.registerPlugin({
      id: pluginId,
      name: 'UI Settings',
    });
  },

  bootstrap() {},
};
