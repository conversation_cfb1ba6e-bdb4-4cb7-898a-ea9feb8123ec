import SettingsPage from './pages/Settings';
import pluginId from './pluginId';

export default {
  register(app) {
    app.addMenuLink({
      to: `/plugins/${pluginId}`,
      icon: () => <span>⚙️</span>,
      intlLabel: {
        id: `${pluginId}.plugin.name`,
        defaultMessage: 'UI Settings',
      },
      Component: SettingsPage,
      permissions: [], // quan trọng để Strapi render link
    });

    app.registerPlugin({
      id: pluginId,
      name: 'UI Settings',
    });
  },

  bootstrap() {},
};
