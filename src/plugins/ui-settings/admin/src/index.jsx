import SettingsPage from './pages/Settings/index.jsx';
import pluginId from './pluginId.js';

console.log('UI Settings plugin loaded');

export default {
  register(app) {
    console.log('Registering UI Settings plugin', { app, pluginId });
    console.log('Available app methods:', Object.keys(app));

    try {
      // Add as a main menu link (more visible for testing)
      app.addMenuLink({
        to: `/plugins/${pluginId}`,
        icon: () => '⚙️',
        intlLabel: {
          id: `${pluginId}.plugin.name`,
          defaultMessage: 'UI Settings',
        },
        Component: SettingsPage,
        permissions: [], // Empty permissions array allows all users
      });
      console.log('Menu link added successfully');

      // Register the plugin
      app.registerPlugin({
        id: pluginId,
        name: 'UI Settings',
      });
      console.log('Plugin registered successfully');
    } catch (error) {
      console.error('Error registering UI Settings plugin:', error);
    }
  },

  bootstrap() {
    console.log('UI Settings plugin bootstrapped');
  },
};
