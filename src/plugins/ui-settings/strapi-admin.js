export default {
  register() {
    // Register function required for Strapi 5
  },

  async bootstrap(app) {
    try {
      const res = await fetch('/ui-settings/config');
      const config = await res.json();

      if (config.pageTitle) {
        document.title = config.pageTitle;
      }
      if (config.favicon) {
        let link = document.querySelector("link[rel~='icon']");
        if (!link) {
          link = document.createElement('link');
          link.rel = 'icon';
          document.getElementsByTagName('head')[0].appendChild(link);
        }
        link.href = config.favicon;
      }
    } catch (e) {
      console.warn('UI Settings plugin: could not load config');
    }
  },
};
