module.exports = ({ strapi }) => ({
  // Register routes
  routes: [
    {
      method: 'GET',
      path: '/config',
      handler: 'ui-settings.getConfig',
      config: { auth: false }, // public
    },
    {
      method: 'POST',
      path: '/config',
      handler: 'ui-settings.setConfig',
      config: { policies: ['admin::isAuthenticatedAdmin'] },
    },
  ],

  controllers: {
    'ui-settings': {
      async getConfig(ctx) {
        const store = strapi.store({ type: 'plugin', name: 'ui-settings' });
        const settings = (await store.get({ key: 'config' })) || {};
        ctx.body = settings;
      },

      async setConfig(ctx) {
        const { pageTitle, pageSubtitle, favicon } = ctx.request.body;
        const store = strapi.store({ type: 'plugin', name: 'ui-settings' });
        await store.set({
          key: 'config',
          value: { pageTitle, pageSubtitle, favicon },
        });
        ctx.body = { ok: true };
      },
    },
  },
});
