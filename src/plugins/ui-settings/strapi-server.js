module.exports = ({ strapi }) => {
  console.log('UI Settings server plugin loaded');

  return {
    register() {
      console.log('UI Settings server: register called');
    },

    bootstrap() {
      console.log('UI Settings server: bootstrap called');
    },

    // Register routes
    routes: [
      {
        method: 'GET',
        path: '/config',
        handler: 'ui-settings.getConfig',
        config: { auth: false }, // public
      },
      {
        method: 'POST',
        path: '/config',
        handler: 'ui-settings.setConfig',
        config: { policies: ['admin::isAuthenticatedAdmin'] },
      },
    ],

    controllers: {
      'ui-settings': {
        async getConfig(ctx) {
          try {
            const store = strapi.store({ type: 'plugin', name: 'ui-settings' });
            const settings = (await store.get({ key: 'config' })) || {};
            ctx.body = settings;
          } catch (error) {
            strapi.log.error('Error getting UI settings config:', error);
            ctx.body = {};
          }
        },

        async setConfig(ctx) {
          try {
            const { pageTitle, pageSubtitle, favicon } = ctx.request.body;
            const store = strapi.store({ type: 'plugin', name: 'ui-settings' });
            await store.set({
              key: 'config',
              value: { pageTitle, pageSubtitle, favicon },
            });
            ctx.body = { ok: true };
          } catch (error) {
            strapi.log.error('Error setting UI settings config:', error);
            ctx.badRequest('Failed to save settings');
          }
        },
      },
    },
  };
};
