'use strict';

const fs = require('fs');
const { parse } = require('csv-parse');

module.exports = () => ({
  async importFromCSV(csvFilePath, collectionUid) {
    const parser = fs
      .createReadStream(csvFilePath)
      .pipe(parse({ columns: true, skip_empty_lines: true }));

    for await (const record of parser) {
      // check service have method createOrUpdate
      if (strapi.service(collectionUid).createOrUpdate) {
        await strapi.service(collectionUid).createOrUpdate(record);
      }
    }

    return true;
  },
});
