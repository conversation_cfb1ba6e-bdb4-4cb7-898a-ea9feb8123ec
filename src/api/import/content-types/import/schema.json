{"kind": "collectionType", "collectionName": "imports", "info": {"singularName": "import", "pluralName": "imports", "displayName": "Import"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"filePath": {"type": "string"}, "importStatus": {"type": "string"}, "total": {"type": "integer"}, "success": {"type": "integer"}, "error": {"type": "integer"}, "fieldMappings": {"type": "json"}}}