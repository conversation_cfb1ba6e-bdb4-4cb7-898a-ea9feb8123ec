'use strict';

const fs = require('fs');

// @ts-ignore
const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::import.import', ({ strapi }) => ({
  async uploadCSVImport(ctx) {
    const user = ctx.state.user;
    try {
      // Get collection UID from request body or query
      const { module } = ctx.request.body || ctx.query;
      if (!module) {
        return ctx.badRequest('Module is required');
      }

      const { fieldMappings, totalRows } = ctx.request.body;
      if (!fieldMappings) {
        return ctx.badRequest('Field mapping is required');
      }

      // Validate that the collection exists
      const contentType = await strapi
        .service('api::metadata.metadata')
        .getContentTypeFromCollectionName(module);
      if (!contentType) {
        return ctx.badRequest(`Collection ${module} does not exist`);
      }

      // Check if file is uploaded
      const { files } = ctx.request;
      if (!files || Object.keys(files).length === 0) {
        return ctx.badRequest('No CSV file uploaded');
      }

      // Support any field name; fall back to the first file-like entry
      const uploadedFile =
        files.file ||
        files.files ||
        files.csv ||
        files.upload ||
        files.data ||
        files[Object.keys(files)[0]];
      console.log('uploadedFile full object:', uploadedFile);
      console.log('uploadedFile properties:', {
        originalFilename: uploadedFile.originalFilename,
        filepath: uploadedFile.filepath,
        mimetype: uploadedFile.mimetype,
        size: uploadedFile.size,
        // Also check alternative property names
        name: uploadedFile.name,
        path: uploadedFile.path,
        type: uploadedFile.type,
        // Check if it's an array
        isArray: Array.isArray(uploadedFile),
        // Check all enumerable properties
        allKeys: Object.keys(uploadedFile),
      });

      // Handle case where uploadedFile might be an array
      const fileObj = Array.isArray(uploadedFile) ? uploadedFile[0] : uploadedFile;

      // Get the correct file path - try different property names
      const filePath = fileObj.filepath || fileObj.path || fileObj.tempFilePath;
      const fileName = fileObj.originalFilename || fileObj.name || fileObj.filename;
      const fileType = fileObj.mimetype || fileObj.type || fileObj.mimeType;

      if (!filePath) {
        return ctx.badRequest('File path is not available');
      }

      if (!fileName) {
        return ctx.badRequest('File name is not available');
      }

      // Validate file type
      if (!fileName.toLowerCase().endsWith('.csv')) {
        return ctx.badRequest('Only CSV files are allowed');
      }

      // If fieldMappings is a string (from multipart forms), parse it
      let normalizedFieldMappings = fieldMappings;
      if (typeof normalizedFieldMappings === 'string') {
        try {
          normalizedFieldMappings = JSON.parse(normalizedFieldMappings);
        } catch (e) {
          return ctx.badRequest('Field mapping must be valid JSON');
        }
      }

      // Use Strapi's upload plugin to save the file
      const uploadService = strapi.plugin('upload').service('upload');
      // Upload file using Strapi's upload service
      const [uploadedFileRecord] = await uploadService.upload({
        data: {
          fileInfo: {
            name: fileName,
            caption: `CSV import for ${module}`,
            alternativeText: `CSV import file`,
          },
        },
        files: fileObj,
      });

      // Process the CSV file using the existing import service
      // Use the temporary file path for processing since it's more reliable
      const importService = strapi.service('api::import.import');
      const result = await importService.importFromCSV(
        filePath,
        contentType.uid,
        normalizedFieldMappings,
        {
          totalRows,
          uploadedFile: uploadedFileRecord,
          assigned_user: user,
        }
      );

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      return ctx.send({
        message: 'CSV file uploaded and processed successfully',
        file: {
          id: uploadedFileRecord.id,
          name: uploadedFileRecord.name,
          url: uploadedFileRecord.url,
          size: uploadedFileRecord.size,
        },
        importResult: result,
        collectionUid: contentType.uid,
        collectionName: contentType.collectionName,
      });
    } catch (error) {
      strapi.log.error('Error uploading CSV file:', error);

      // Clean up temporary file in case of error
      const errorFiles = ctx.request.files || {};
      const errorFileObjBase =
        errorFiles.file ||
        errorFiles.files ||
        errorFiles.csv ||
        errorFiles.upload ||
        errorFiles.data ||
        (Object.keys(errorFiles).length ? errorFiles[Object.keys(errorFiles)[0]] : undefined);
      const errorFileObj = Array.isArray(errorFileObjBase) ? errorFileObjBase[0] : errorFileObjBase;
      const errorFilePath =
        errorFileObj?.filepath || errorFileObj?.path || errorFileObj?.tempFilePath;
      if (errorFilePath && fs.existsSync(errorFilePath)) {
        try {
          fs.unlinkSync(errorFilePath);
        } catch (cleanupError) {
          strapi.log.error('Error cleaning up temporary file:', cleanupError);
        }
      }

      return ctx.internalServerError(
        'An error occurred while uploading the CSV file: ' + error.message
      );
    }
  },
}));
