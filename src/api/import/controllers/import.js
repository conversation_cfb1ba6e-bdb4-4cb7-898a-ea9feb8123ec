'use strict';

const fs = require('fs');

// @ts-ignore
const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::import.import', ({ strapi }) => ({
  async uploadCSVImport(ctx) {
    try {
      // Get collection UID from request body or query
      const { module } = ctx.request.body || ctx.query;
      if (!module) {
        return ctx.badRequest('Module is required');
      }

      const { fieldMappings, totalRows } = ctx.request.body;
      if (!fieldMappings) {
        return ctx.badRequest('Field mapping is required');
      }

      // Validate that the collection exists
      const contentType = await strapi
        .service('api::metadata.metadata')
        .getContentTypeFromCollectionName(module);
      if (!contentType) {
        return ctx.badRequest(`Collection ${module} does not exist`);
      }

      // Check if file is uploaded
      const { files } = ctx.request;
      if (!files || !files.file) {
        return ctx.badRequest('No CSV file uploaded');
      }

      const uploadedFile = files.file;
      console.log('uploadedFile properties:', {
        originalFilename: uploadedFile.originalFilename,
        filepath: uploadedFile.filepath,
        mimetype: uploadedFile.mimetype,
        size: uploadedFile.size,
        // Also check alternative property names
        name: uploadedFile.name,
        path: uploadedFile.path,
        type: uploadedFile.type,
      });

      // Get the correct file path - try different property names
      const filePath = uploadedFile.filepath || uploadedFile.path;
      const fileName = uploadedFile.originalFilename || uploadedFile.name;
      const fileType = uploadedFile.mimetype || uploadedFile.type;

      if (!filePath) {
        return ctx.badRequest('File path is not available');
      }

      if (!fileName) {
        return ctx.badRequest('File name is not available');
      }

      // Validate file type
      if (!fileName.toLowerCase().endsWith('.csv')) {
        return ctx.badRequest('Only CSV files are allowed');
      }

      // Use Strapi's upload plugin to save the file
      const uploadService = strapi.plugin('upload').service('upload');

      // Read file buffer
      const fileBuffer = fs.readFileSync(filePath);

      // Create file object for Strapi upload
      const fileData = {
        name: fileName,
        type: fileType || 'text/csv',
        size: uploadedFile.size,
        buffer: fileBuffer,
      };

      // Upload file using Strapi's upload service
      const [uploadedFileRecord] = await uploadService.upload({
        data: {
          fileInfo: {
            name: fileName,
            caption: `CSV import for ${module}`,
            alternativeText: `CSV import file`,
          },
        },
        files: fileData,
      });

      // Process the CSV file using the existing import service
      // Use the temporary file path for processing since it's more reliable
      const importService = strapi.service('api::import.import');
      const result = await importService.importFromCSV(filePath, contentType.uid, fieldMappings, {
        totalRows: uploadedFile.size,
      });

      // Clean up temporary file
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      return ctx.send({
        message: 'CSV file uploaded and processed successfully',
        file: {
          id: uploadedFileRecord.id,
          name: uploadedFileRecord.name,
          url: uploadedFileRecord.url,
          size: uploadedFileRecord.size,
        },
        importResult: result,
        collectionUid: contentType.uid,
        collectionName: contentType.collectionName,
      });
    } catch (error) {
      strapi.log.error('Error uploading CSV file:', error);

      // Clean up temporary file in case of error
      const errorFilePath = ctx.request.files?.file?.filepath || ctx.request.files?.file?.path;
      if (errorFilePath && fs.existsSync(errorFilePath)) {
        try {
          fs.unlinkSync(errorFilePath);
        } catch (cleanupError) {
          strapi.log.error('Error cleaning up temporary file:', cleanupError);
        }
      }

      return ctx.internalServerError(
        'An error occurred while uploading the CSV file: ' + error.message
      );
    }
  },
}));
