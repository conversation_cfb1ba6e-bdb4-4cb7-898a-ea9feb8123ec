'use strict';

const fs = require('fs');

// @ts-ignore
const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::import.import', ({ strapi }) => ({
  async uploadCSVImport(ctx) {
    try {
      // Get collection UID from request body or query
      const { module } = ctx.request.body || ctx.query;
      if (!module) {
        return ctx.badRequest('Module is required');
      }

      const { fieldMappings } = ctx.request.body;
      if (!fieldMappings) {
        return ctx.badRequest('Field mapping is required');
      }

      // Validate that the collection exists
      const contentType = await strapi
        .service('api::metadata.metadata')
        .getContentTypeFromCollectionName(module);
      if (!contentType) {
        return ctx.badRequest(`Collection ${module} does not exist`);
      }

      // Check if file is uploaded
      const { files } = ctx.request;
      if (!files || !files.file) {
        return ctx.badRequest('No CSV file uploaded');
      }

      console.log('files', files);
      const uploadedFile = files.file;

      // Validate file type
      if (!uploadedFile.originalFilename.toLowerCase().endsWith('.csv')) {
        return ctx.badRequest('Only CSV files are allowed');
      }

      // Use Strapi's upload plugin to save the file
      const uploadService = strapi.plugin('upload').service('upload');

      // Read file buffer
      const fileBuffer = fs.readFileSync(uploadedFile.filepath);

      // Create file object for Strapi upload
      const fileData = {
        name: uploadedFile.originalFilename,
        type: uploadedFile.mimetype || 'text/csv',
        size: uploadedFile.size,
        buffer: fileBuffer,
      };

      // Upload file using Strapi's upload service
      const [uploadedFileRecord] = await uploadService.upload({
        data: {
          fileInfo: {
            name: uploadedFile.originalFilename,
            caption: `CSV import for ${module}`,
            alternativeText: `CSV import file`,
          },
        },
        files: fileData,
      });

      // Process the CSV file using the existing import service
      // Use the temporary file path for processing since it's more reliable
      const importService = strapi.service('api::import.import');
      const result = await importService.importFromCSV(
        uploadedFile.filepath,
        contentType.uid,
        fieldMappings
      );

      // Clean up temporary file
      if (fs.existsSync(uploadedFile.path)) {
        fs.unlinkSync(uploadedFile.path);
      }

      return ctx.send({
        message: 'CSV file uploaded and processed successfully',
        file: {
          id: uploadedFileRecord.id,
          name: uploadedFileRecord.name,
          url: uploadedFileRecord.url,
          size: uploadedFileRecord.size,
        },
        importResult: result,
        collectionUid: contentType.uid,
        collectionName: contentType.collectionName,
      });
    } catch (error) {
      strapi.log.error('Error uploading CSV file:', error);

      // Clean up temporary file in case of error
      if (ctx.request.files?.file?.path && fs.existsSync(ctx.request.files.file.path)) {
        try {
          fs.unlinkSync(ctx.request.files.file.path);
        } catch (cleanupError) {
          strapi.log.error('Error cleaning up temporary file:', cleanupError);
        }
      }

      return ctx.internalServerError(
        'An error occurred while uploading the CSV file: ' + error.message
      );
    }
  },
}));
