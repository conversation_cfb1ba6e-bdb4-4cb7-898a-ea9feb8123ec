import React from 'react';

// UI Settings component using React.createElement
const UISettingsPage = () => {
  const [pageTitle, setPageTitle] = React.useState('');
  const [favicon, setFavicon] = React.useState('');
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    // Load settings
    fetch('/ui-settings/config')
      .then((res) => res.json())
      .then((data) => {
        setPageTitle(data.pageTitle || '');
        setFavicon(data.favicon || '');
      })
      .catch(console.error);
  }, []);

  const handleSave = async () => {
    setLoading(true);
    try {
      await fetch('/ui-settings/config', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pageTitle, favicon }),
      });
      alert('Settings saved!');
    } catch (error) {
      alert('Error saving settings');
      console.error(error);
    }
    setLoading(false);
  };

  return React.createElement('div', { style: { padding: '2rem' } }, [
    React.createElement('h1', { key: 'title' }, 'UI Settings'),
    React.createElement('div', { key: 'title-field', style: { marginBottom: '1rem' } }, [
      React.createElement('label', { key: 'title-label' }, 'Page Title:'),
      React.createElement('br', { key: 'title-br' }),
      React.createElement('input', {
        key: 'title-input',
        value: pageTitle,
        onChange: (e) => setPageTitle(e.target.value),
        style: { width: '300px', padding: '8px' },
      }),
    ]),
    React.createElement('div', { key: 'favicon-field', style: { marginBottom: '1rem' } }, [
      React.createElement('label', { key: 'favicon-label' }, 'Favicon URL:'),
      React.createElement('br', { key: 'favicon-br' }),
      React.createElement('input', {
        key: 'favicon-input',
        value: favicon,
        onChange: (e) => setFavicon(e.target.value),
        style: { width: '300px', padding: '8px' },
      }),
    ]),
    React.createElement(
      'button',
      {
        key: 'save-button',
        onClick: handleSave,
        disabled: loading,
      },
      loading ? 'Saving...' : 'Save Settings'
    ),
  ]);
};

console.log('🎯 Admin app.js loading...');

const config = {
  locales: [],
};

const bootstrap = (app) => {
  console.log('🎯 Admin bootstrap called with app:', app);
  console.log('🎯 Available methods:', Object.keys(app));

  // Try to add UI Settings to the admin panel
  try {
    if (app.addMenuLink) {
      app.addMenuLink({
        to: '/ui-settings',
        icon: () => React.createElement('span', {}, '⚙️'),
        intlLabel: {
          id: 'ui-settings.plugin.name',
          defaultMessage: 'UI Settings',
        },
        Component: UISettingsPage,
        permissions: [],
      });
      console.log('🎯 Menu link added via admin extension');
    }

    if (app.addSettingsLink) {
      app.addSettingsLink({
        id: 'ui-settings',
        to: '/settings/ui-settings',
        intlLabel: {
          id: 'ui-settings.plugin.name',
          defaultMessage: 'UI Settings',
        },
        Component: UISettingsPage,
      });
      console.log('🎯 Settings link added via admin extension');
    }
  } catch (error) {
    console.error('🎯 Error adding UI Settings:', error);
  }
};

export default {
  config,
  bootstrap,
};
